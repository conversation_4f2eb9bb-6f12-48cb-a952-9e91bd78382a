#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL数据库启动和监控脚本
确保数据库服务持续运行
"""

import subprocess
import time
import psycopg2
import os
import signal
import sys
from pathlib import Path

class PostgreSQLManager:
    def __init__(self):
        self.pg_ctl_path = r"C:\Program Files\PostgreSQL\16\bin\pg_ctl.exe"
        self.data_dir = Path(__file__).parent
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'tradefusion',
            'user': 'postgres',
            'password': 'ymjatTUU520'
        }
        self.monitoring = False
        
    def is_postgres_running(self):
        """检查PostgreSQL是否正在运行"""
        try:
            conn = psycopg2.connect(**self.db_config, connect_timeout=3)
            conn.close()
            return True
        except:
            return False
    
    def start_postgres(self):
        """启动PostgreSQL服务"""
        try:
            print("🚀 启动PostgreSQL数据库...")
            
            # 切换到数据目录
            os.chdir(str(self.data_dir))
            
            # 启动PostgreSQL
            cmd = [self.pg_ctl_path, "-D", ".", "start"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ PostgreSQL启动成功")
                
                # 等待服务完全启动
                for i in range(10):
                    if self.is_postgres_running():
                        print("✅ 数据库连接测试成功")
                        return True
                    time.sleep(1)
                
                print("⚠️ PostgreSQL已启动但连接测试失败")
                return False
            else:
                print(f"❌ PostgreSQL启动失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ PostgreSQL启动超时")
            return False
        except Exception as e:
            print(f"❌ 启动PostgreSQL时发生错误: {e}")
            return False
    
    def stop_postgres(self):
        """停止PostgreSQL服务"""
        try:
            print("🛑 停止PostgreSQL数据库...")
            
            os.chdir(str(self.data_dir))
            cmd = [self.pg_ctl_path, "-D", ".", "stop", "-m", "fast"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ PostgreSQL已停止")
                return True
            else:
                print(f"⚠️ PostgreSQL停止时有警告: {result.stderr}")
                return True  # 通常停止警告不是严重问题
                
        except Exception as e:
            print(f"❌ 停止PostgreSQL时发生错误: {e}")
            return False
    
    def restart_postgres(self):
        """重启PostgreSQL服务"""
        print("🔄 重启PostgreSQL数据库...")
        self.stop_postgres()
        time.sleep(2)
        return self.start_postgres()
    
    def get_status(self):
        """获取PostgreSQL状态"""
        try:
            os.chdir(str(self.data_dir))
            cmd = [self.pg_ctl_path, "-D", ".", "status"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return "运行中", result.stdout
            else:
                return "已停止", result.stderr
                
        except Exception as e:
            return "未知", str(e)
    
    def monitor_postgres(self, check_interval=30):
        """监控PostgreSQL服务，自动重启"""
        print(f"👁️ 开始监控PostgreSQL服务 (检查间隔: {check_interval}秒)")
        print("按 Ctrl+C 停止监控")
        
        self.monitoring = True
        consecutive_failures = 0
        
        # 信号处理
        def signal_handler(sig, frame):
            print("\n🛑 收到停止信号，正在退出监控...")
            self.monitoring = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            while self.monitoring:
                if self.is_postgres_running():
                    if consecutive_failures > 0:
                        print("✅ PostgreSQL服务已恢复正常")
                        consecutive_failures = 0
                    
                    print(f"✅ {time.strftime('%H:%M:%S')} - PostgreSQL运行正常")
                else:
                    consecutive_failures += 1
                    print(f"❌ {time.strftime('%H:%M:%S')} - PostgreSQL服务异常 (连续失败: {consecutive_failures})")
                    
                    if consecutive_failures >= 2:
                        print("🔧 尝试重启PostgreSQL服务...")
                        if self.restart_postgres():
                            consecutive_failures = 0
                        else:
                            print("❌ 重启失败，将在下次检查时重试")
                
                # 等待下次检查
                for _ in range(check_interval):
                    if not self.monitoring:
                        break
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            print("\n🛑 监控已停止")
        
        print("👋 PostgreSQL监控程序退出")

def main():
    """主函数"""
    manager = PostgreSQLManager()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "start":
            manager.start_postgres()
        elif command == "stop":
            manager.stop_postgres()
        elif command == "restart":
            manager.restart_postgres()
        elif command == "status":
            status, info = manager.get_status()
            print(f"PostgreSQL状态: {status}")
            print(f"详细信息: {info}")
        elif command == "monitor":
            # 先确保服务运行
            if not manager.is_postgres_running():
                manager.start_postgres()
            manager.monitor_postgres()
        else:
            print("用法: python 启动并监控PostgreSQL.py [start|stop|restart|status|monitor]")
    else:
        # 默认行为：启动并简单检查
        print("🎯 PostgreSQL管理器")
        print("=" * 50)
        
        status, info = manager.get_status()
        print(f"当前状态: {status}")
        
        if not manager.is_postgres_running():
            print("数据库未运行，正在启动...")
            manager.start_postgres()
        else:
            print("✅ 数据库运行正常")
        
        print("\n💡 可用命令:")
        print("  python 启动并监控PostgreSQL.py start    - 启动数据库")
        print("  python 启动并监控PostgreSQL.py stop     - 停止数据库")
        print("  python 启动并监控PostgreSQL.py restart  - 重启数据库")
        print("  python 启动并监控PostgreSQL.py status   - 查看状态")
        print("  python 启动并监控PostgreSQL.py monitor  - 持续监控")

if __name__ == "__main__":
    main()
