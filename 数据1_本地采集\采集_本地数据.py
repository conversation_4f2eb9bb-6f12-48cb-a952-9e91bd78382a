#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import struct
import sys
import pytz
import time
import signal
from pathlib import Path

# 重新设置项目根目录
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent  # 回退到TradeFusion目录

# 确保项目根目录在系统路径中
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from 公共模块.交易日期 import get_trading_date
    from 公共模块.配置管理 import get_config
except ImportError as e:
    sys.exit(1)

from datetime import datetime

# 获取全局配置实例
config_manager = get_config()

# TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("采集_本地数据")

# 循环控制变量
running = True
cycle_count = 0
success_count = 0
fail_count = 0
total_data_processed = 0

def signal_handler(signum, frame):
    """信号处理器：优雅退出"""
    global running
    logger.记录模块执行("接收到退出信号，正在优雅退出")
    logger.记录模块执行(f"最终汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败", total_data_processed)
    running = False

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def log_success(count):
    """记录成功状态 - 符合TradeFusion统一日志标准"""
    logger.记录模块执行("本地数据采集完成", count)

def log_error(error_msg):
    """记录错误状态 - 符合TradeFusion统一日志标准"""
    logger.记录错误(error_msg)

def _write_to_height_table(trading_date, results):
    """写入个股连板高度表，并自动触发后续处理模块"""
    # {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
    import psycopg2
    try:
        # {{ AURA-X: Modify - 使用统一配置管理而非硬编码. Approval: 寸止(ID:1737734400). }}
        # 使用统一配置管理
        config = get_config()
        db_config = config.get('database')
        conn = psycopg2.connect(**db_config)

        # 根据依赖关系分析，本模块应直接写入个股连板高度表
        # 先删除当日数据
        # {{ AURA-X: Delete - 删除重复的trading_date获取，使用传入的参数. Approval: 寸止(ID:1737734400). }}
        cursor = conn.cursor()
        cursor.execute('DELETE FROM "个股连板高度表" WHERE "日期" = %s', (trading_date,))

        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        # 插入新数据到个股连板高度表
        cursor.executemany('''
            INSERT INTO "个股连板高度表" (
                "日期", "股票代码", "连板高度", "涨停评分", "涨停时间",
                "一字板", "T字板", "黄金换手", "成交金额"
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        ''', results)

        conn.commit()
        conn.close()

        # 🔄 个股连板高度表写入完成后，自动触发第2层数据处理模块
        _trigger_layer2_processing()

    except Exception as e:
        logger.记录错误("写入个股连板高度表失败", e)

def _trigger_layer2_processing():
    """触发第2层数据处理模块（板块涨停表）"""
    try:
        # 导入第2层数据处理模块
        from 数据库2_板块层统计.板块涨停表 import main as 板块涨停_main

        logger.记录模块执行("自动触发第2层数据处理模块")

        # 调用板块涨停表处理函数
        result = 板块涨停_main()

        if result:
            logger.记录模块执行("第2层数据处理模块执行成功")
        else:
            logger.记录错误("第2层数据处理模块执行失败")

    except Exception as e:
        logger.记录错误("触发第2层数据处理模块异常", e)


def log_heartbeat(success_count, fail_count, total_data):
    """心跳汇总 - 符合TradeFusion统一日志标准"""
    logger.记录模块执行(f"心跳汇总 - {success_count}次成功，{fail_count}次失败", total_data)

def parse_lbgd_data():
    """带日志记录的本地数据解析函数"""
    def data_processing():
        try:
            trading_date = get_trading_date()
            date_str = str(trading_date)

            beijing_tz = pytz.timezone('Asia/Shanghai')
            naive_date = datetime.strptime(date_str, "%Y%m%d")
            target_date = beijing_tz.localize(naive_date.replace(hour=8,  minute=0, second=0))
            target_timestamp = int(target_date.astimezone(pytz.utc).timestamp())

            # 获取数据目录配置
            data_dirs = config_manager.get_data_dirs()

            results = []
            current_time = time.time()
            timeout_seconds = config_manager.get_file_timeout_seconds()

            # 增强目录读取监控
            file_dicts = {}
            for dir_type, dir_path in data_dirs.items():
                try:
                    files = {os.path.splitext(f)[0]: f
                            for f in os.listdir(dir_path)
                            if f.endswith(".dat")}
                    file_dicts[dir_type] = files
                except Exception as e:
                    log_error(f"目录读取失败 {dir_type}: {e}")
                    file_dicts[dir_type] = {}
 
            # 获取所有股票代码的并集（处理任何有数据的股票）
            all_stocks = set()
            for stocks in file_dicts.values():
                all_stocks.update(stocks.keys())

            if not all_stocks:
                return {
                    'success': False,
                    'data_count': 0,
                    'error': 'No stock data found'
                }

            # 数据处理流程
            for stock_code in all_stocks:
                # 为每个数据类型创建文件路径（如果存在）
                file_paths = {}
                for dir_type, files in file_dicts.items():
                    if stock_code in files:
                        file_paths[dir_type] = os.path.join(data_dirs[dir_type], files[stock_code])

                # 检查是否至少有一个数据文件
                if not file_paths:
                    continue

                # 文件时效性检查 - 只检查连板高度文件
                try:
                    # 检查连板高度文件是否存在
                    if 'lbgd' not in file_paths:
                        continue

                    # 检查连板高度文件是否过期
                    lbgd_file_time = os.path.getmtime(file_paths['lbgd'])
                    timeout_seconds = config_manager.get_file_timeout_seconds()
                    if current_time - lbgd_file_time > timeout_seconds:
                        continue
                except Exception as e:
                    continue

                # 数据解析逻辑
                data_values = {key: None for key in data_dirs.keys()}  # 初始化所有字段为None

                for key, path in file_paths.items():
                    try:
                        with open(path, "rb") as f:
                            data = f.read()
                            value = None
                            for i in range(0, len(data), 8):
                                if i+8 > len(data):
                                    continue
                                timestamp = int.from_bytes(data[i:i+4], 'little')
                                if timestamp == target_timestamp:
                                    value = struct.unpack('<f', data[i+4:i+8])[0]
                                    break
                            data_values[key] = value
                    except Exception as e:
                        pass

                # 至少需要连板高度数据才进行入库
                if data_values.get('lbgd') is not None:
                    results.append((
                        trading_date, stock_code,
                        data_values.get('lbgd'), data_values.get('ztdp'),
                        data_values.get('ztsj'), data_values.get('yzb'),
                        data_values.get('tzb'), data_values.get('hjhs'),
                        data_values.get('cjje')
                    ))

            # 记录采集成功状态
            log_success(len(results))

            # 写入个股连板高度表，并自动触发后续处理模块
            _write_to_height_table(trading_date, results)

            return {
                'success': True,
                'data_count': len(results),
                'results': results
            }

        except Exception as e:
            # 按设计方案记录错误状态
            log_error(f"数据处理失败: {str(e)}")
            return {
                'success': False,
                'data_count': 0,
                'error': str(e)
            }

    # 执行数据处理
    return data_processing()

def process_lbgd_data():
    """处理本地数据的主函数"""
    try:
        result = parse_lbgd_data()
        return result and result.get('success', False)
    except Exception as e:
        log_error(f"处理本地数据失败: {str(e)}")
        return False

def run_continuous():
    """循环运行模式：每12秒执行一次"""
    global running, cycle_count, success_count, fail_count, total_data_processed

    # 移除开始日志，只保留心跳汇总

    try:
        while running:
            cycle_count += 1
            start_time = time.time()

            # 移除每次执行开始日志

            try:
                # 执行数据处理
                result = parse_lbgd_data()

                if result and result.get('success', False):
                    success_count += 1
                    data_count = result.get('data_count', 0)
                    total_data_processed += data_count
                    # 移除每次执行成功日志，只保留心跳汇总
                else:
                    fail_count += 1
                    error_msg = result.get('error', '未知错误') if result else '执行失败'
                    logger.记录错误(f"第{cycle_count}次执行失败: {error_msg}")

            except Exception as e:
                fail_count += 1
                log_error(f"第{cycle_count}次执行异常: {str(e)}")

            # 每10个周期输出一次心跳汇总
            if cycle_count % 10 == 0:
                log_heartbeat(success_count, fail_count, total_data_processed)

            # 如果还要继续运行，等待12秒
            if running:
                execution_time = time.time() - start_time
                sleep_time = max(0, 12 - execution_time)

                if sleep_time > 0:
                    # 移除等待日志
                    time.sleep(sleep_time)
                else:
                    logger.记录错误(f"执行时间{execution_time:.1f}秒超过12秒间隔")

    except KeyboardInterrupt:
        logger.记录模块执行("用户中断，正在退出")
    except Exception as e:
        log_error(f"循环运行异常: {str(e)}")
    finally:
        logger.记录模块执行(f"循环结束汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败", total_data_processed)

if __name__ == "__main__":
    # 设置控制台编码
    import sys
    if sys.platform.startswith('win'):
        import os
        os.system('chcp 65001 > nul')  # 设置为UTF-8编码

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        # 单次执行模式
        # 移除单次执行开始日志
        parse_lbgd_data()
    else:
        # 循环执行模式（默认）
        run_continuous()