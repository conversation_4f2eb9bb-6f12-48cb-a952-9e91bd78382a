#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion人气临时表管理模块
功能：管理东财和同花人气数据的临时表操作
作者：TradeFusion团队
创建时间：2025-07-12
"""

# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import psycopg2
import psycopg2.extras
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Tuple, Optional
import logging

# 配置日志（按照设计方案：数据库操作模块应该静默，只在错误时输出）
logger = logging.getLogger(__name__)
logger.setLevel(logging.ERROR)  # 只记录错误，不记录INFO

class 人气临时表管理器:
    """人气数据临时表管理器"""

    def __init__(self, db_config: dict = None):
        """
        初始化临时表管理器
        Args:
            db_config: PostgreSQL数据库配置，如果为None则使用默认配置
        """
        # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
        # {{ AURA-X: Modify - 使用统一配置管理而非硬编码. Approval: 寸止(ID:1737734400). }}
        if db_config is None:
            try:
                from 公共模块.配置管理 import get_config
                config = get_config()
                self.db_config = config.get('database')
            except ImportError:
                # 备用配置
                self.db_config = {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'tradefusion',
                    'user': 'postgres',
                    'password': 'ymjatTUU520'
                }
        else:
            self.db_config = db_config

        self.conn = None
        self._connect()
        self._ensure_temp_tables()
    
    def _connect(self):
        """连接数据库"""
        # {{ AURA-X: Modify - 修改为PostgreSQL连接方法. Approval: 寸止(ID:1737734400). }}
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.conn.autocommit = False
            logger.info(f"<人气临时表管理> PostgreSQL数据库连接成功")
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> PostgreSQL数据库连接失败: {str(e)}")
            raise
    
    def _ensure_temp_tables(self):
        """确保临时表存在"""
        try:
            # {{ AURA-X: Modify - 使用PostgreSQL版本的SQL文件. Approval: 寸止(ID:1737734400). }}
            # 读取SQL结构文件
            sql_file = Path(__file__).parent.parent / "数据库0_实体模块/人气临时表结构_PostgreSQL.sql"
            
            if sql_file.exists():
                with open(sql_file, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
                
                # {{ AURA-X: Modify - PostgreSQL不支持executescript，改为逐条执行. Approval: 寸止(ID:1737734400). }}
                # {{ AURA-X: Modify - 改进SQL语句分割和过滤. Approval: 寸止(ID:1737734400). }}
                # 执行SQL创建表结构
                cursor = self.conn.cursor()
                # 分割SQL语句并逐条执行，过滤空语句和注释
                sql_statements = []
                for stmt in sql_content.split(';'):
                    stmt = stmt.strip()
                    if stmt and not stmt.startswith('--') and stmt != '':
                        sql_statements.append(stmt)

                for statement in sql_statements:
                    if statement.strip():  # 再次确保不是空语句
                        cursor.execute(statement)
                self.conn.commit()
                logger.info(f"<人气临时表管理> 临时表结构初始化完成")
            else:
                logger.warning(f"⚠️ <人气临时表管理> SQL结构文件不存在: {sql_file}")
                self._create_temp_tables_manually()
                
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 临时表初始化失败: {str(e)}")
            raise
    
    def _create_temp_tables_manually(self):
        """手动创建临时表（备用方案）"""
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL表创建语法. Approval: 寸止(ID:1737734400). }}
            # 创建东财人气临时表
            cursor = self.conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS "临时表_东财人气" (
                    id SERIAL PRIMARY KEY,
                    "股票代码" TEXT NOT NULL,
                    "股票名称" TEXT NOT NULL,
                    "人气排名" INTEGER NOT NULL,
                    "采集时间" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    "处理状态" TEXT DEFAULT '待处理',
                    "备注" TEXT
                )
            """)

            # 创建同花人气临时表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS "临时表_同花人气" (
                    id SERIAL PRIMARY KEY,
                    "股票代码" TEXT NOT NULL,
                    "股票名称" TEXT NOT NULL,
                    "人气排名" INTEGER NOT NULL,
                    "采集时间" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    "处理状态" TEXT DEFAULT '待处理',
                    "备注" TEXT
                )
            """)
            
            self.conn.commit()
            logger.info(f"<人气临时表管理> 手动创建临时表完成")
            
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 手动创建临时表失败: {str(e)}")
            raise
    
    def 清空东财临时表(self):
        """清空东财人气临时表"""
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute('DELETE FROM "临时表_东财人气"')
            deleted_count = cursor.rowcount
            self.conn.commit()
            logger.info(f"<人气临时表管理> 清空东财临时表，删除 {deleted_count} 条记录")
            return deleted_count
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 清空东财临时表失败: {str(e)}")
            raise
    
    def 清空同花临时表(self):
        """清空同花人气临时表"""
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute('DELETE FROM "临时表_同花人气"')
            deleted_count = cursor.rowcount
            self.conn.commit()
            logger.info(f"<人气临时表管理> 清空同花临时表，删除 {deleted_count} 条记录")
            return deleted_count
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 清空同花临时表失败: {str(e)}")
            raise
    
    def 写入东财数据(self, stock_data: List[Tuple[str, str, int]]):
        """
        写入东财人气数据到临时表
        Args:
            stock_data: [(股票代码, 股票名称, 人气排名), ...]
        """
        try:
            # 先清空临时表
            self.清空东财临时表()
            
            # 批量插入新数据
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.executemany(
                'INSERT INTO "临时表_东财人气" ("股票代码", "股票名称", "人气排名") VALUES (%s, %s, %s)',
                stock_data
            )
            self.conn.commit()
            
            logger.info(f"<人气临时表管理> 东财数据写入完成，共 {len(stock_data)} 条记录")
            return len(stock_data)
            
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 东财数据写入失败: {str(e)}")
            raise
    
    def 写入同花数据(self, stock_data: List[Tuple[str, str, int]]):
        """
        写入同花人气数据到临时表
        Args:
            stock_data: [(股票代码, 股票名称, 人气排名), ...]
        """
        try:
            # 先清空临时表
            self.清空同花临时表()
            
            # 批量插入新数据
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.executemany(
                'INSERT INTO "临时表_同花人气" ("股票代码", "股票名称", "人气排名") VALUES (%s, %s, %s)',
                stock_data
            )
            self.conn.commit()
            
            logger.info(f"<人气临时表管理> 同花数据写入完成，共 {len(stock_data)} 条记录")
            return len(stock_data)
            
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 同花数据写入失败: {str(e)}")
            raise
    
    def 读取东财待处理数据(self) -> List[Tuple[str, str, int]]:
        """读取东财待处理数据"""
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT "股票代码", "股票名称", "人气排名"
                FROM "临时表_东财人气"
                WHERE "处理状态" = '待处理'
                ORDER BY "人气排名"
            ''')
            data = cursor.fetchall()
            logger.info(f"<人气临时表管理> 读取东财待处理数据，共 {len(data)} 条记录")
            return data
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 读取东财数据失败: {str(e)}")
            raise
    
    def 读取同花待处理数据(self) -> List[Tuple[str, str, int]]:
        """读取同花待处理数据"""
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT "股票代码", "股票名称", "人气排名"
                FROM "临时表_同花人气"
                WHERE "处理状态" = '待处理'
                ORDER BY "人气排名"
            ''')
            data = cursor.fetchall()
            logger.info(f"<人气临时表管理> 读取同花待处理数据，共 {len(data)} 条记录")
            return data
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 读取同花数据失败: {str(e)}")
            raise
    
    def 标记东财数据已处理(self):
        """标记东财数据为已处理"""
        try:
            # {{ AURA-X: Modify - 修复PostgreSQL游标调用错误. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute('''
                UPDATE "临时表_东财人气"
                SET "处理状态" = '已处理'
                WHERE "处理状态" = '待处理'
            ''')
            updated_count = cursor.rowcount
            self.conn.commit()
            logger.info(f"<人气临时表管理> 标记东财数据已处理，共 {updated_count} 条记录")
            return updated_count
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 标记东财数据失败: {str(e)}")
            raise
    
    def 标记同花数据已处理(self):
        """标记同花数据为已处理"""
        try:
            # {{ AURA-X: Modify - 修复PostgreSQL游标调用错误. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute('''
                UPDATE "临时表_同花人气"
                SET "处理状态" = '已处理'
                WHERE "处理状态" = '待处理'
            ''')
            updated_count = cursor.rowcount
            self.conn.commit()
            logger.info(f"<人气临时表管理> 标记同花数据已处理，共 {updated_count} 条记录")
            return updated_count
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 标记同花数据失败: {str(e)}")
            raise
    
    def 获取临时表状态(self) -> dict:
        """获取临时表状态信息"""
        try:
            status = {}

            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            # 东财临时表状态
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT "处理状态", COUNT(*) as count
                FROM "临时表_东财人气"
                GROUP BY "处理状态"
            ''')
            status['东财'] = dict(cursor.fetchall())

            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            # 同花临时表状态
            cursor.execute('''
                SELECT "处理状态", COUNT(*) as count
                FROM "临时表_同花人气"
                GROUP BY "处理状态"
            ''')
            status['同花'] = dict(cursor.fetchall())

            # 选股宝原始数据状态
            cursor.execute('''
                SELECT "处理状态", COUNT(*) as count
                FROM "临时表_选股宝原始"
                GROUP BY "处理状态"
            ''')
            status['选股宝原始'] = dict(cursor.fetchall())

            # 选股宝清洗数据状态
            cursor.execute('''
                SELECT "处理状态", COUNT(*) as count
                FROM "临时表_选股宝清洗"
                GROUP BY "处理状态"
            ''')
            status['选股宝清洗'] = dict(cursor.fetchall())

            logger.info(f"<人气临时表管理> 临时表状态查询完成")
            return status

        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 获取临时表状态失败: {str(e)}")
            raise
    
    def 写入选股宝原始数据(self, raw_data: str):
        """
        写入选股宝原始数据到临时表
        Args:
            raw_data: 原始网页文本数据
        """
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            # 先清空临时表
            cursor = self.conn.cursor()
            cursor.execute('DELETE FROM "临时表_选股宝原始"')
            deleted_count = cursor.rowcount
            if deleted_count > 0:
                logger.info(f"<人气临时表管理> 清空选股宝原始临时表，删除 {deleted_count} 条记录")

            # 插入新数据
            cursor.execute(
                'INSERT INTO "临时表_选股宝原始" ("原始数据") VALUES (%s)',
                (raw_data,)
            )
            self.conn.commit()

            logger.info(f"<人气临时表管理> 选股宝原始数据写入完成，共 1 条记录")
            return 1

        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 选股宝原始数据写入失败: {str(e)}")
            raise

    def 读取选股宝原始待处理数据(self) -> Optional[str]:
        """读取选股宝原始待处理数据"""
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT "原始数据"
                FROM "临时表_选股宝原始"
                WHERE "处理状态" = '待处理'
                ORDER BY 采集时间 DESC
                LIMIT 1
            ''')
            result = cursor.fetchone()

            if result:
                logger.info(f"<人气临时表管理> 读取选股宝原始待处理数据成功")
                return result[0]
            else:
                logger.info(f"<人气临时表管理> 选股宝原始临时表无待处理数据")
                return None

        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 读取选股宝原始数据失败: {str(e)}")
            raise

    def 写入选股宝清洗数据(self, cleaned_data: List[Tuple]):
        """
        写入选股宝清洗数据到临时表
        Args:
            cleaned_data: [(日期, 股票代码, 板块名称, 板块涨幅, 板块消息, 个股解读), ...]
        """
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            # 先清空临时表
            cursor = self.conn.cursor()
            cursor.execute('DELETE FROM "临时表_选股宝清洗"')
            deleted_count = cursor.rowcount
            if deleted_count > 0:
                logger.info(f"<人气临时表管理> 清空选股宝清洗临时表，删除 {deleted_count} 条记录")

            # 批量插入新数据，明确设置处理状态为待处理
            cursor.executemany(
                'INSERT INTO "临时表_选股宝清洗" ("日期", "股票代码", "板块名称", "板块涨幅", "板块消息", "个股解读", "处理状态") VALUES (%s, %s, %s, %s, %s, %s, %s)',
                [(date, code, sector, change, msg, analysis, '待处理') for date, code, sector, change, msg, analysis in cleaned_data]
            )
            self.conn.commit()

            logger.info(f"<人气临时表管理> 选股宝清洗数据写入完成，共 {len(cleaned_data)} 条记录")
            return len(cleaned_data)

        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 选股宝清洗数据写入失败: {str(e)}")
            raise

    def 读取选股宝清洗待处理数据(self) -> List[Tuple]:
        """读取选股宝清洗待处理数据"""
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT "日期", "股票代码", "板块名称", "板块涨幅", "板块消息", "个股解读"
                FROM "临时表_选股宝清洗"
                WHERE "处理状态" = '待处理'
                ORDER BY 日期 DESC, 股票代码
            ''')
            data = cursor.fetchall()
            logger.info(f"<人气临时表管理> 读取选股宝清洗待处理数据，共 {len(data)} 条记录")
            return data
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 读取选股宝清洗数据失败: {str(e)}")
            raise

    def 标记选股宝原始数据已处理(self):
        """标记选股宝原始数据为已处理"""
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute('''
                UPDATE "临时表_选股宝原始"
                SET "处理状态" = '已处理'
                WHERE "处理状态" = '待处理'
            ''')
            updated_count = cursor.rowcount
            self.conn.commit()
            logger.info(f"<人气临时表管理> 标记选股宝原始数据已处理，共 {updated_count} 条记录")
            return updated_count
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 标记选股宝原始数据失败: {str(e)}")
            raise

    def 标记选股宝清洗数据已处理(self):
        """标记选股宝清洗数据为已处理"""
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute('''
                UPDATE "临时表_选股宝清洗"
                SET "处理状态" = '已处理'
                WHERE "处理状态" = '待处理'
            ''')
            updated_count = cursor.rowcount
            self.conn.commit()
            logger.info(f"<人气临时表管理> 标记选股宝清洗数据已处理，共 {updated_count} 条记录")
            return updated_count
        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 标记选股宝清洗数据失败: {str(e)}")
            raise

    def 清理历史数据(self, days: int = 7):
        """清理指定天数前的历史数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            cutoff_str = cutoff_date.strftime('%Y-%m-%d %H:%M:%S')

            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            # 清理东财历史数据
            cursor = self.conn.cursor()
            cursor.execute('''
                DELETE FROM "临时表_东财人气"
                WHERE "处理状态" = '已处理'
                AND "采集时间" < %s
            ''', (cutoff_str,))
            cursor1_rowcount = cursor.rowcount

            # 清理同花历史数据
            cursor.execute('''
                DELETE FROM "临时表_同花人气"
                WHERE "处理状态" = '已处理'
                AND "采集时间" < %s
            ''', (cutoff_str,))
            cursor2_rowcount = cursor.rowcount

            # 清理选股宝原始数据
            cursor.execute('''
                DELETE FROM "临时表_选股宝原始"
                WHERE "处理状态" = '已处理'
                AND "采集时间" < %s
            ''', (cutoff_str,))
            cursor3_rowcount = cursor.rowcount

            # 清理选股宝清洗数据
            cursor.execute('''
                DELETE FROM "临时表_选股宝清洗"
                WHERE "处理状态" = '已处理'
                AND "采集时间" < %s
            ''', (cutoff_str,))
            cursor4_rowcount = cursor.rowcount

            self.conn.commit()

            # {{ AURA-X: Modify - 修改为使用保存的rowcount. Approval: 寸止(ID:1737734400). }}
            total_deleted = cursor1_rowcount + cursor2_rowcount + cursor3_rowcount + cursor4_rowcount
            logger.info(f"<人气临时表管理> 清理 {days} 天前历史数据，共删除 {total_deleted} 条记录")
            return total_deleted

        except Exception as e:
            logger.error(f"✗ <人气临时表管理> 清理历史数据失败: {str(e)}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            logger.info(f"<人气临时表管理> 数据库连接已关闭")

# 便捷函数
def 获取临时表管理器() -> 人气临时表管理器:
    """获取临时表管理器实例"""
    return 人气临时表管理器()

if __name__ == "__main__":
    # 测试代码
    manager = 获取临时表管理器()
    status = manager.获取临时表状态()
    print("临时表状态:", status)
    manager.close()
