# ===================== a_ths_ie.py =====================
import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from 数据2_网络采集.浏览器管理器 import get_browser_manager

# 使用TradeFusion统一日志标准

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 必须先初始化路径
def get_project_paths():
    """获取项目路径配置"""
    script_path = Path(__file__).absolute()
    project_root = script_path.parent.parent
    
    paths = {
        'root': project_root,
        'drivers': project_root / 'drivers',
        'logs': project_root / 'logs',
        'crawler': project_root / '数据2_网络采集',
        'common': project_root / '公共模块'
    }
    
    # 确保目录存在
    for path in paths.values():
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
    
    return paths

PATHS = get_project_paths()
if str(PATHS['root']) not in sys.path:
    sys.path.insert(0, str(PATHS['root']))

logger = 获取日志器("人气_同花采集")

# 现在可以安全导入其他模块
import time

# 导入公共模块
try:
    from 公共模块.交易日期 import get_trading_date
except ImportError as e:
    print(f"导入公共模块失败: {e}")
    print(f"Python路径: {sys.path}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"请确保以下目录存在并包含必要模块: {PATHS['root']}")
    print(f"目录内容: {list(PATHS['root'].glob('*'))}")
    sys.exit(1)

# 使用配置的路径
LOG_PATH = PATHS['logs'] / "playwright.log"

class THS_Crawler:
    def __init__(self):
        self.all_data = []

    def get_stock_codes(self, stocks):
        """从抓取的股票数据中提取股票代码和排名"""
        # 不再从公共模块.加代码前缀导入process_stock_code_prefix
        
        processed_codes = []
        for stock in stocks:
            # 获取股票名称和排名
            stock_name = stock['name']
            rank = stock['rank']
            
            # 通过股票名称查找对应的股票代码
            stock_code = self.find_stock_code_by_name(stock_name)
            
            if stock_code:
                # 直接使用数据库查询得到的股票代码，不再添加前缀
                processed_codes.append((stock_code, stock_name, rank))
            # 未找到股票代码的情况直接忽略
        
        return processed_codes
    
    def find_stock_code_by_name(self, stock_name):
        """通过股票名称查找股票代码 - 优化版本"""
        try:
            # {{ AURA-X: Modify - 使用统一配置管理替代硬编码. Approval: 寸止(ID:1737734400). }}
            # 使用统一配置管理获取数据库配置
            import psycopg2
            from datetime import datetime, timedelta
            from 公共模块.配置管理 import get_config

            # 获取统一数据库配置
            config = get_config()
            db_config = config.get('database')

            # 连接数据库
            conn = psycopg2.connect(**db_config)
            cursor = conn.cursor()

            # 优化查询：只查询最近30天的数据，减少扫描范围
            thirty_days_ago = int((datetime.now() - timedelta(days=30)).strftime('%Y%m%d'))

            query = '''
            SELECT "股票代码" FROM "个股人气表"
            WHERE "股票名称" = %s
            AND "日期" >= %s
            ORDER BY "日期" DESC
            LIMIT 1
            '''

            cursor.execute(query, (stock_name, thirty_days_ago))
            result = cursor.fetchone()

            # 关闭连接
            conn.close()

            if result:
                return result[0]
            else:
                # 如果最近30天没有，再查询全部历史数据（兜底方案）
                return self._find_stock_code_fallback(stock_name)

        except Exception as e:
            return None

    def _find_stock_code_fallback(self, stock_name):
        """兜底查询：查询全部历史数据"""
        try:
            # {{ AURA-X: Modify - 使用统一配置管理替代硬编码. Approval: 寸止(ID:1737734400). }}
            import psycopg2
            from 公共模块.配置管理 import get_config

            # 获取统一数据库配置
            config = get_config()
            db_config = config.get('database')

            conn = psycopg2.connect(**db_config)
            cursor = conn.cursor()

            query = '''
            SELECT "股票代码" FROM "个股人气表"
            WHERE "股票名称" = %s
            ORDER BY "日期" DESC
            LIMIT 1
            '''

            cursor.execute(query, (stock_name,))
            result = cursor.fetchone()
            conn.close()

            return result[0] if result else None

        except Exception as e:
            return None

    def run(self):
        """运行爬虫，使用持久化浏览器"""
        try:
            # 使用持久化浏览器管理器
            browser_manager = get_browser_manager("tonghuashun")

            # 访问目标网页
            url = 'https://eq.10jqka.com.cn/frontend/thsTopRank/index.html?fontzoom=no&client_userid=R9Po6&share_hxapp=gsc&share_action=webpage_share.hot_list_1748317879184&back_source=qqhy#/'
            page = browser_manager.get_page(url, force_refresh=True)
            time.sleep(3)  # 等待页面加载

            # 尝试点击热股标签
            try:
                hot_tab = page.get_by_text("热股")
                if hot_tab:
                    hot_tab.click()
                    time.sleep(1)
            except Exception as e:
                pass

            # 使用键盘滚动页面以加载全部100支股票
            for i in range(20):  # 20次PageDown足够加载完整数据
                page.keyboard.press("PageDown")
                time.sleep(0.8)  # 给足够时间让懒加载触发

                # 每5次滚动检查一次数据
                if (i + 1) % 5 == 0:
                    current_count = page.evaluate('''() => {
                        const items = Array.from(document.querySelectorAll('div[data-v-4b7d5302], div[data-v-2e888cf8]'));
                        const stockData = {};
                        const maxRank = 100;

                        for (let i = 0; i < items.length; i++) {
                            const item = items[i];
                            try {
                                const rankElement = item.querySelector('div.THSMF-M.bold.flex');
                                if (rankElement) {
                                    const rank = parseInt(rankElement.textContent.trim());
                                    if (!isNaN(rank) && rank <= maxRank) {
                                        stockData[rank] = true;
                                    }
                                }
                            } catch (e) {}
                        }

                        return Object.keys(stockData).length;
                    }''')

                    # 如果已经获取到100支股票，则停止滚动
                    if current_count >= 100:
                        break

            # 提取股票数据
            stocks = page.evaluate('''() => {
                const items = Array.from(document.querySelectorAll('div[data-v-4b7d5302], div[data-v-2e888cf8]'));
                const stockItems = [];
                const maxRank = 100;
                const processedRanks = new Set();

                for (let i = 0; i < items.length; i++) {
                    const item = items[i];
                    try {
                        const rankElement = item.querySelector('div.THSMF-M.bold.flex');
                        if (rankElement) {
                            const rank = parseInt(rankElement.textContent.trim());
                            if (!isNaN(rank) && rank <= maxRank && !processedRanks.has(rank)) {
                                const nameElement = item.querySelector('span.ellipsis');
                                if (nameElement) {
                                    const name = nameElement.textContent.trim();
                                    stockItems.push({rank, name});
                                    processedRanks.add(rank);
                                }
                            }
                        }
                    } catch (e) {}
                }

                return stockItems;
            }''')

            # 注意：不再关闭浏览器，保持持久化连接
            
            # 按排名排序
            stocks.sort(key=lambda x: x['rank'])

            # 检查是否获取了足够的股票
            if len(stocks) < 50:
                logger.记录错误(f"只获取到 {len(stocks)} 支股票，少于最低要求50支")
                return [], None

            # 处理股票代码
            processed_data = self.get_stock_codes(stocks)

            trading_date = get_trading_date()  # 统一获取交易日
            if processed_data:
                # 写入临时表（替代CSV文件）
                try:
                    from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
                    temp_manager = 获取临时表管理器()
                    temp_manager.写入同花数据(processed_data)
                    temp_manager.close()

                    # 移除中间步骤日志，只保留最终完成日志

                except Exception as e:
                    logger.记录错误(f"写入临时表失败", e)
                    return [], None

            # 返回处理后的数据，包含股票代码和名称
            return [(code, name) for code, name, _ in processed_data], trading_date

        except Exception as e:
            logger.记录错误(f"网站数据采集失败", e)
            import traceback
            traceback.print_exc()
            return [], None

def _trigger_popularity_fusion():
    """触发个股人气表融合模块"""
    try:
        # 导入人气融合模块
        from 数据2_网络采集.个股人气表 import main as 人气融合_main

        # logger.info(f"🔄 [人气_同花采集] 自动触发人气融合模块...")

        # 调用人气融合函数
        result = 人气融合_main()

        if result:
            # logger.info(f"✅ [人气_同花采集] 人气融合模块执行成功")
            pass
        else:
            logger.记录错误(f"人气融合模块执行失败")

    except Exception as e:
        logger.记录错误(f"触发人气融合模块异常", e)

def main():
    try:
        # 移除开始日志，只保留完成日志

        crawler = THS_Crawler()
        result = crawler.run()

        # 检查返回值是否有效
        if result is None:
            logger.记录错误(f"网站数据采集失败，未获取到数据")
            return None

        processed_data, date = result

        # 记录采集结果（只负责采集，不调用数据库）
        if processed_data:
            logger.记录模块执行(f"网站数据采集完成", len(processed_data))

            # 🔄 同花数据采集完成后，立即触发人气融合模块
            _trigger_popularity_fusion()
        else:
            logger.记录错误(f"网站数据采集失败，未获取到数据")
        return processed_data, date
    except Exception as e:
        logger.记录错误(f"程序异常", e)
        import traceback
        traceback.print_exc()
        return [], None

def run_continuous():
    """循环运行模式：每60秒执行一次"""
    import time

    cycle_count = 0
    success_count = 0
    fail_count = 0

    # logger.info(f"🔄 [人气_同花采集] 循环模式启动 - 60秒间隔")

    try:
        while True:
            cycle_count += 1
            start_time = time.time()

            try:
                main()
                success_count += 1
                # logger.info(f"💓 [人气_同花采集] 第{cycle_count}次执行成功")
            except Exception as e:
                fail_count += 1
                logger.记录错误(f"第{cycle_count}次执行失败", e)

            # 计算等待时间（60秒）
            execution_time = time.time() - start_time
            sleep_time = max(0, 60 - execution_time)

            if sleep_time > 0:
                time.sleep(sleep_time)
            else:
                logger.记录错误(f"执行时间{execution_time:.1f}秒超过60秒间隔")

    except KeyboardInterrupt:
        # logger.info(f"<人气_同花采集> 用户中断，正在退出...")
        pass
    except Exception as e:
        logger.记录错误(f"循环运行异常", e)
    finally:
        # logger.info(f"💓 [人气_同花采集] 循环结束汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败")
        pass

if __name__ == "__main__":
    import sys

    # {{ AURA-X: Modify - 修复双重执行问题，统一执行逻辑. Approval: 寸止(ID:1737734400). }}
    # 检查是否为单次执行模式
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        # 单次执行模式
        main()
    else:
        # 循环执行模式（默认）
        run_continuous()

