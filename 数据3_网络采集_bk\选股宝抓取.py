import sys
import os
from pathlib import Path

# 动态获取项目根目录
if getattr(sys, 'frozen', False):
    PROJECT_ROOT = Path(sys.executable).parent
else:
    PROJECT_ROOT = Path(__file__).absolute().parent.parent

# 验证工作目录
if not PROJECT_ROOT.exists():
    print(f"错误：项目根目录不存在 - {PROJECT_ROOT}")
    sys.exit(1)

# 添加项目根目录到Python路径
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

import csv
import time
from datetime import datetime, time as dt_time

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("选股宝抓取")

# 使用持久化浏览器管理器
try:
    from 数据2_网络采集.浏览器管理器 import get_browser_manager
except ImportError:
    # 如果直接导入失败，尝试相对导入
    from 浏览器管理器 import get_browser_manager

# ========== 全局配置 ==========
# 定义路径
LOGS_PATH = PROJECT_ROOT / "logs"
# 确保目录存在
LOGS_PATH.mkdir(parents=True, exist_ok=True)
TARGET_URL = "https://xuangutong.com.cn/top-gainer"

# {{ AURA-X: Add - 添加禁止运行时段配置（8:40-9:25集合竞价时间）. Approval: 寸止(ID:1737734400). }}
# 禁止运行时段配置
FORBIDDEN_START_TIME = dt_time(8, 40)  # 8:40
FORBIDDEN_END_TIME = dt_time(9, 25)    # 9:25

def is_forbidden_time():
    """检查当前时间是否在禁止运行时段（8:40-9:25）"""
    current_time = datetime.now().time()

    # 检查是否在禁止时段内
    if FORBIDDEN_START_TIME <= current_time <= FORBIDDEN_END_TIME:
        return True
    return False

def get_time_until_allowed():
    """获取距离允许运行时间的秒数"""
    now = datetime.now()
    current_time = now.time()

    # 如果在禁止时段内，计算到9:25的剩余时间
    if FORBIDDEN_START_TIME <= current_time <= FORBIDDEN_END_TIME:
        # 构造今天9:25的datetime
        end_datetime = datetime.combine(now.date(), FORBIDDEN_END_TIME)
        time_diff = end_datetime - now
        return int(time_diff.total_seconds()) + 1  # 加1秒确保完全过了禁止时段

    return 0

# ========== 核心功能类 ==========
class StockCrawler:
    def __init__(self):
        self._init_browser()
        self._bootstrap()

    def _init_browser(self):
        """初始化持久化浏览器"""

        try:
            # 使用持久化浏览器管理器
            self._browser_manager = get_browser_manager("xuangubao")

        except Exception as e:

            raise

    def _bootstrap(self):
        """加载目标网页"""

        try:
            # 使用持久化浏览器获取页面，自动刷新
            self._page = self._browser_manager.get_page(TARGET_URL, force_refresh=True)
            # 选股宝网站的股票数据容器
            self._page.wait_for_selector("#nuxt-layout-container > div > div.topgainer-content > div.topgainer-content-left > div",
                                        state="visible",
                                        timeout=30000)

        except Exception as e:
            self._error_handler(f"系统初始化失败: {str(e)}")

    def fetch_data(self, retries=3):
        """抓取股票数据"""

        for attempt in range(1, retries + 1):
            try:
                # {{ AURA-X: Modify - 增强错误日志记录，添加详细的执行状态信息. Approval: 寸止(ID:1735002400). }}
                logger.记录模块执行(f"开始第{attempt}次数据抓取尝试")

                # 使用持久化浏览器的刷新功能
                self._browser_manager.refresh_page()
                time.sleep(5 + attempt * 2)  # 增加等待时间，随重试次数递增

                # 检查页面是否完全加载
                self._page.wait_for_load_state("domcontentloaded")
                logger.记录模块执行("页面加载完成")

                # 检查目标元素是否存在
                data_selector = "#nuxt-layout-container > div > div.topgainer-content > div.topgainer-content-left > div"
                element = self._page.wait_for_selector(data_selector, state="visible", timeout=15000)

                if not element:
                    raise ValueError("未找到数据元素")

                # 获取元素文本内容
                text_content = element.inner_text()

                # 验证元素内容不为空
                if not text_content.strip():
                    raise ValueError("抓取到的数据为空")

                logger.记录模块执行(f"第{attempt}次数据抓取成功", len(text_content))
                return text_content.strip()

            except Exception as e:
                # {{ AURA-X: Modify - 增强错误处理，记录详细错误信息和重试策略. Approval: 寸止(ID:1735002400). }}
                error_msg = f"第{attempt}次抓取失败: {str(e)}"
                logger.记录错误(error_msg)

                if attempt == retries:
                    logger.记录错误(f"所有{retries}次重试均失败，开始保存调试信息")

                    # 保存截图和页面源码用于调试
                    try:
                        screenshot_path = LOGS_PATH / "xgb_error.png"
                        self._page.screenshot(path=str(screenshot_path))
                        logger.记录模块执行("错误截图已保存", str(screenshot_path))

                        html_path = LOGS_PATH / "xgb_error.html"
                        with open(html_path, "w", encoding="utf-8") as f:
                            f.write(self._page.content())
                        logger.记录模块执行("错误页面源码已保存", str(html_path))

                    except Exception as ss_error:
                        logger.记录错误("保存调试信息失败", ss_error)

                    return None

                # {{ AURA-X: Modify - 改进重试策略，根据错误类型采用不同的恢复方法. Approval: 寸止(ID:1735002400). }}
                logger.记录模块执行(f"准备第{attempt + 1}次重试，采用智能恢复策略")

                # 增加更智能的刷新策略
                if "timeout" in str(e).lower():
                    logger.记录模块执行("检测到超时错误，执行页面重载")
                    self._page.reload()
                elif "element not found" in str(e).lower() or "element not visible" in str(e).lower():
                    logger.记录模块执行("检测到元素缺失，重新导航到目标页面")
                    self._page.goto(TARGET_URL, wait_until="networkidle")
                else:
                    logger.记录模块执行("检测到其他错误，执行标准页面刷新")
                    self._browser_manager.refresh_page()

                time.sleep(3)

    def _error_handler(self, msg):
        """错误处理"""

        try:
            self._page.reload()
        except:
            pass
        time.sleep(10)

    def run_service(self):
        """运行爬虫服务"""
        try:
            # {{ AURA-X: Modify - 添加服务运行状态日志，便于问题追踪. Approval: 寸止(ID:1735002400). }}
            logger.记录模块执行("开始运行爬虫服务")

            data = self.fetch_data()
            if data:
                logger.记录模块执行("数据抓取成功，开始保存数据")
                self.save_data(data)
                logger.记录模块执行("爬虫服务执行完成")
                return True
            else:
                logger.记录错误("数据抓取失败，爬虫服务执行失败")
                return False
        except Exception as e:
            # {{ AURA-X: Add - 添加异常捕获，防止未处理的异常导致服务失败. Approval: 寸止(ID:1735002400). }}
            logger.记录错误("爬虫服务执行异常", e)
            return False
        finally:
            # 在run_service中不关闭浏览器，由main函数统一关闭
            pass

    # ========== 数据保存方法 ==========
    def save_data(self, raw_data):
        """保存数据到临时表（主要）和CSV文件（备用）"""
        try:
            # 主要方案：写入临时表
            try:
                from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
                temp_manager = 获取临时表管理器()
                temp_manager.写入选股宝原始数据(raw_data)
                temp_manager.close()

                # 网络采集成功日志（符合TradeFusion统一日志标准）
                logger.记录模块执行("网站数据采集完成", len(raw_data))

                # 🔄 数据写入完成后，自动触发清洗模块
                self._trigger_cleaning_module()

            except Exception as e:
                logger.记录错误("写入临时表失败", e)
                # 备用方案：保存CSV文件
                SAVE_PATH = PROJECT_ROOT / "数据3_网络采集_bk" / "选股宝数据.csv"
                if SAVE_PATH.exists():
                    SAVE_PATH.unlink()

                with SAVE_PATH.open("w", encoding="utf-8", newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(["股票名称", "最新价", "涨幅", "换手率"])
                    count = 0
                    for line in raw_data.split('\n'):
                        if line.strip():
                            writer.writerow(line.split())
                            count += 1
                # 备用方案成功日志（符合TradeFusion统一日志标准）
                logger.记录模块执行("网站数据采集完成(备用CSV)", count)

        except Exception as e:
            logger.记录错误("数据保存失败", e)
            raise

    def _trigger_cleaning_module(self):
        """触发选股宝清洗模块"""
        try:
            # 导入清洗模块的主要处理函数
            from 数据3_网络采集_bk.选股宝清洗 import run_cleaner_from_temp_table

            logger.记录模块执行("自动触发清洗模块")

            # 调用清洗函数
            result = run_cleaner_from_temp_table()

            if result is not None:
                logger.记录模块执行("清洗模块执行成功", result)
                # {{ AURA-X: Delete - 删除重复调用，清洗模块已自动触发下游模块. Approval: 寸止(ID:1735002000). }}
                # 清洗模块会自动触发下游处理模块，无需重复调用
            else:
                logger.记录错误("清洗模块执行失败")

        except Exception as e:
            logger.记录错误("触发清洗模块异常", e)

    # {{ AURA-X: Delete - 删除重复调用方法，避免与清洗模块的自动触发冲突. Approval: 寸止(ID:1735002000). }}
    # _trigger_next_modules 方法已删除，因为清洗模块会自动触发下游处理模块

    def close_browser(self):
        """清理浏览器资源（持久化浏览器不需要手动关闭）"""
        try:
            # 持久化浏览器由browser_manager统一管理，这里不需要手动关闭
            pass
        except Exception as e:
            pass


# ========== 主程序 ==========
def main() -> Path:
    crawler = None
    try:
        # {{ AURA-X: Add - 添加禁止运行时段检查. Approval: 寸止(ID:1737734400). }}
        # 检查是否在禁止运行时段
        if is_forbidden_time():
            current_time = datetime.now().strftime("%H:%M:%S")
            wait_seconds = get_time_until_allowed()
            logger.记录模块执行(f"当前时间{current_time}在禁止运行时段(8:40-9:25)，跳过执行")
            logger.记录模块执行(f"距离允许运行还有{wait_seconds}秒")
            return None

        # {{ AURA-X: Modify - 增强主函数错误处理和状态跟踪. Approval: 寸止(ID:1735002400). }}
        logger.记录模块执行("开始初始化选股宝爬虫")
        crawler = StockCrawler()
        logger.记录模块执行("爬虫初始化成功")

        if crawler.run_service():
            # 数据抓取完成，写入临时表后由触发器系统自动处理后续流程
            logger.记录模块执行("网站数据采集完成 - 数据已写入临时表")
            return True  # 返回成功状态
        else:
            logger.记录错误("爬虫服务执行失败")
            return None
    except Exception as e:
        # {{ AURA-X: Modify - 改进异常处理，记录详细错误信息. Approval: 寸止(ID:1735002400). }}
        logger.记录错误("选股宝爬虫主函数执行异常", e)
        import traceback
        traceback.print_exc()
        return None
    finally:
        # 只在这里关闭浏览器，避免重复关闭
        if crawler:
            try:
                logger.记录模块执行("开始清理浏览器资源")
                crawler.close_browser()
                logger.记录模块执行("浏览器资源清理完成")
            except Exception as e:
                # 捕获并打印错误，但不抛出异常
                logger.记录错误("浏览器资源清理失败", e)

def run_continuous():
    """循环运行模式：每60秒执行一次"""
    import time

    cycle_count = 0
    success_count = 0
    fail_count = 0

    logger.记录模块执行("循环模式启动 - 60秒间隔")

    try:
        while True:
            cycle_count += 1
            start_time = time.time()

            # {{ AURA-X: Add - 在循环中添加禁止时段检查和智能等待. Approval: 寸止(ID:1737734400). }}
            # 检查是否在禁止运行时段
            if is_forbidden_time():
                wait_seconds = get_time_until_allowed()
                current_time = datetime.now().strftime("%H:%M:%S")
                logger.记录模块执行(f"第{cycle_count}次循环：当前时间{current_time}在禁止时段，等待{wait_seconds}秒")

                # 智能等待：如果等待时间较长，则直接跳到允许时间
                if wait_seconds > 60:  # 如果需要等待超过1分钟
                    time.sleep(wait_seconds)
                    continue  # 等待结束后重新开始循环
                else:
                    # 等待时间较短，正常计入60秒循环
                    time.sleep(wait_seconds)

            try:
                result_path = main()
                if result_path:
                    success_count += 1
                    logger.记录模块执行(f"第{cycle_count}次执行成功")
                elif result_path is None and is_forbidden_time():
                    # 如果是因为禁止时段返回None，不计入失败
                    logger.记录模块执行(f"第{cycle_count}次执行跳过（禁止时段）")
                else:
                    fail_count += 1
                    logger.记录错误(f"第{cycle_count}次执行失败")
            except Exception as e:
                fail_count += 1
                logger.记录错误(f"第{cycle_count}次执行异常", e)

            # 计算等待时间（60秒）
            execution_time = time.time() - start_time
            sleep_time = max(0, 60 - execution_time)

            if sleep_time > 0:
                time.sleep(sleep_time)
            else:
                logger.记录错误(f"执行时间{execution_time:.1f}秒超过60秒间隔")

    except KeyboardInterrupt:
        logger.记录模块执行("用户中断，正在退出")
    except Exception as e:
        logger.记录错误("循环运行异常", e)
    finally:
        logger.记录模块执行(f"循环结束汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        # 单次执行模式
        result_path = main()
        if result_path:
            pass
        else:
            sys.exit(1)
    else:
        # 循环执行模式（默认）
        run_continuous()