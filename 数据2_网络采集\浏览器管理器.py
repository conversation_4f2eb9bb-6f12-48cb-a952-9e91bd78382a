"""
浏览器管理工具类
用于统一管理Playwright浏览器实例的生命周期
"""

import time
import logging
from pathlib import Path
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from typing import Optional, Dict, Any


class BrowserManager:
    """浏览器管理器 - 单例模式管理浏览器实例"""

    _instances: Dict[str, 'BrowserManager'] = {}
    _global_playwright = None  # 全局Playwright实例
    
    def __new__(cls, name: str = "default"):
        if name not in cls._instances:
            cls._instances[name] = super().__new__(cls)
        return cls._instances[name]
    
    def __init__(self, name: str = "default"):
        if hasattr(self, '_initialized'):
            return
        
        self.name = name
        self._playwright = None
        self._browser = None
        self._context = None
        self._page = None
        self._initialized = True
        self._last_health_check = 0
        self._health_check_interval = 30  # 30秒检查一次
        
        # 设置日志 - 只记录ERROR级别，避免干扰业务日志
        self.logger = logging.getLogger(f"BrowserManager_{name}")
        self.logger.setLevel(logging.ERROR)  # 只记录错误，不记录INFO和WARNING
        
    def _setup_logging(self, log_file: Optional[Path] = None):
        """设置日志系统"""
        if log_file:
            handler = logging.FileHandler(log_file, encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            # 保持ERROR级别，不覆盖之前的设置
    
    def is_healthy(self) -> bool:
        """检查浏览器是否健康"""
        try:
            if not self._browser or not self._page:
                return False
            
            # 检查浏览器是否还连接
            if not self._browser.is_connected():
                return False
            
            # 尝试执行简单的JavaScript来测试页面是否响应
            self._page.evaluate("() => document.readyState")
            return True
        except Exception as e:
            self.logger.warning(f"浏览器健康检查失败: {e}")
            return False
    
    def _create_browser(self, headless: bool = True, **kwargs) -> None:
        """创建新的浏览器实例"""
        try:
            self.logger.info("正在创建新的浏览器实例...")

            # 清理旧实例
            self._cleanup()

            # {{ AURA-X: Add - 支持环境变量控制无头模式. Approval: 寸止(ID:**********). }}
            # 检查环境变量是否启用无头模式
            import os
            if os.getenv('BROWSER_HEADLESS', '').lower() in ('true', '1', 'yes'):
                headless = True
                self.logger.info("检测到BROWSER_HEADLESS环境变量，启用无头模式")

            # 使用全局Playwright实例
            if BrowserManager._global_playwright is None:
                BrowserManager._global_playwright = sync_playwright().start()
            self._playwright = BrowserManager._global_playwright
            
            # 默认浏览器配置
            browser_args = [
                "--no-sandbox",
                "--disable-blink-features=AutomationControlled",
                "--force-device-scale-factor=1",
                "--lang=zh-CN",
                "--ignore-certificate-errors"
            ]
            
            # 合并用户提供的参数
            if 'args' in kwargs:
                browser_args.extend(kwargs['args'])
                del kwargs['args']

            # {{ AURA-X: Add - 强制浏览器子进程使用项目虚拟环境. Approval: 寸止(ID:1737734400). }}
            # 设置环境变量，确保浏览器子进程使用项目虚拟环境
            import os
            from pathlib import Path

            # 获取项目根目录和虚拟环境路径
            project_root = Path(__file__).parent.parent
            venv_python = project_root / "venv" / "Scripts" / "python.exe"

            # 准备环境变量
            browser_env = os.environ.copy()
            if venv_python.exists():
                browser_env['PYTHON'] = str(venv_python)
                browser_env['PYTHONPATH'] = str(project_root)
                # 移除可能冲突的PYTHONHOME设置
                if 'PYTHONHOME' in browser_env:
                    del browser_env['PYTHONHOME']
                # 设置PATH，确保虚拟环境的Scripts目录优先
                venv_scripts = str(project_root / "venv" / "Scripts")
                if 'PATH' in browser_env:
                    browser_env['PATH'] = f"{venv_scripts};{browser_env['PATH']}"
                else:
                    browser_env['PATH'] = venv_scripts
                self.logger.info("浏览器子进程环境变量已设置为虚拟环境")

            # {{ AURA-X: Modify - 支持多浏览器选择，默认使用Firefox以降低内存占用. Approval: 寸止(ID:1735003000). }}
            # 检查环境变量选择浏览器类型
            browser_type = os.getenv('BROWSER_TYPE', 'firefox').lower()

            if browser_type == 'firefox':
                self._browser = self._playwright.firefox.launch(
                    headless=headless,
                    env=browser_env,  # Firefox不支持args参数
                    **kwargs
                )
                self.logger.info("使用Firefox浏览器")
            elif browser_type == 'webkit':
                self._browser = self._playwright.webkit.launch(
                    headless=headless,
                    env=browser_env,  # WebKit不支持args参数
                    **kwargs
                )
                self.logger.info("使用WebKit浏览器")
            else:  # 默认chromium
                self._browser = self._playwright.chromium.launch(
                    headless=headless,
                    args=browser_args,
                    env=browser_env,
                    **kwargs
                )
                self.logger.info("使用Chromium浏览器")
            
            # 创建上下文
            context_options = {
                "viewport": {"width": 1366, "height": 768},
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "locale": "zh-CN",
                "timezone_id": "Asia/Shanghai"
            }
            
            self._context = self._browser.new_context(**context_options)
            
            # 添加反检测脚本
            self._context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });
                window.chrome = { runtime: {} };
            """)
            
            # 创建页面
            self._page = self._context.new_page()
            
            self.logger.info("浏览器实例创建成功")
            
        except Exception as e:
            self.logger.error(f"创建浏览器实例失败: {e}")
            self._cleanup()
            raise
    
    def get_page(self, url: Optional[str] = None, force_refresh: bool = True) -> Page:
        """获取页面实例，如果需要则创建浏览器"""
        current_time = time.time()
        
        # 定期健康检查
        if current_time - self._last_health_check > self._health_check_interval:
            self._last_health_check = current_time
            if not self.is_healthy():
                self.logger.warning("浏览器健康检查失败，重新创建...")
                self._create_browser()
        
        # 如果浏览器不存在，创建新的
        if not self._browser or not self._page:
            self._create_browser()
        
        # 如果提供了URL，导航到该页面
        if url:
            if self._page.url != url:
                # URL不同，需要导航到新页面
                self.logger.info(f"导航到页面: {url}")
                self._page.goto(url, wait_until='domcontentloaded', timeout=60000)
            elif force_refresh:
                # URL相同但需要强制刷新
                self.logger.info("刷新当前页面")
                self._page.reload(wait_until='domcontentloaded', timeout=60000)
        
        return self._page
    
    def refresh_page(self) -> None:
        """刷新当前页面"""
        if self._page:
            self.logger.info("刷新页面")
            self._page.reload(wait_until='domcontentloaded', timeout=60000)
    
    def _cleanup(self) -> None:
        """清理浏览器资源"""
        try:
            if self._context:
                self._context.close()
            if self._browser:
                self._browser.close()
            # 不关闭全局Playwright实例，由close_all统一管理
        except Exception as e:
            self.logger.warning(f"清理浏览器资源时出错: {e}")
        finally:
            self._context = None
            self._browser = None
            self._page = None
            # 不设置_playwright = None，保持对全局实例的引用
    
    def close(self) -> None:
        """关闭浏览器（通常在程序退出时调用）"""
        self.logger.info("关闭浏览器管理器")
        self._cleanup()
        
        # 从实例字典中移除
        if self.name in self._instances:
            del self._instances[self.name]
    
    @classmethod
    def close_all(cls) -> None:
        """关闭所有浏览器实例"""
        for manager in list(cls._instances.values()):
            manager.close()
        cls._instances.clear()

        # 关闭全局Playwright实例
        if cls._global_playwright:
            try:
                cls._global_playwright.stop()
            except Exception as e:
                pass
            finally:
                cls._global_playwright = None


# 便捷函数
def get_browser_manager(name: str = "default") -> BrowserManager:
    """获取浏览器管理器实例"""
    return BrowserManager(name)
