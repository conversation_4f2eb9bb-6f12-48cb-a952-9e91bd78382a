# pgAdmin 4 安装配置指南

## 📋 下载信息
- **文件名**: pgadmin4-9.6-x64.exe
- **版本**: pgAdmin 4 v9.6 (最新版本)
- **发布日期**: 2025年7月25日
- **文件大小**: 200.6 MB
- **下载状态**: ✅ 已通过MCP工具成功下载

## 🛠️ 安装步骤

### 1. 运行安装程序
1. 找到下载的文件 `pgadmin4-9.6-x64.exe`
2. 右键点击文件，选择"以管理员身份运行"
3. 如果出现安全警告，点击"是"继续

### 2. 安装向导
1. **欢迎界面**: 点击 "Next"
2. **许可协议**: 勾选 "I accept the agreement"，点击 "Next"
3. **安装路径**: 保持默认路径或选择自定义路径，点击 "Next"
4. **组件选择**: 保持默认选择，点击 "Next"
5. **开始菜单**: 保持默认，点击 "Next"
6. **桌面快捷方式**: 勾选创建桌面图标，点击 "Next"
7. **安装确认**: 点击 "Install" 开始安装
8. **完成安装**: 点击 "Finish"

## 🔧 首次配置

### 1. 启动pgAdmin
- 双击桌面图标或从开始菜单启动pgAdmin 4
- 首次启动会要求设置主密码（Master Password）
- **建议密码**: `TradeFusion2025!` (请记住此密码)

### 2. 连接TradeFusion数据库

#### 步骤1: 添加新服务器
1. 在左侧面板右键点击 "Servers"
2. 选择 "Register" → "Server..."

#### 步骤2: 配置连接信息
**General标签页**:
- Name: `TradeFusion-Local`
- Server group: `Servers`
- Comments: `TradeFusion项目本地PostgreSQL数据库`

**Connection标签页**:
- Host name/address: `localhost`
- Port: `5432`
- Maintenance database: `tradefusion`
- Username: `postgres`
- Password: `ymjatTUU520`
- Save password: ✅ (勾选)

**Advanced标签页**:
- DB restriction: `tradefusion` (可选，限制只显示此数据库)

#### 步骤3: 测试连接
1. 点击 "Save" 保存配置
2. 如果配置正确，左侧会显示 "TradeFusion-Local" 服务器
3. 展开服务器节点查看数据库结构

## 📊 数据库浏览

### 查看表结构
1. 展开 `TradeFusion-Local` → `Databases` → `tradefusion` → `Schemas` → `public` → `Tables`
2. 右键任意表，选择 "View/Edit Data" → "All Rows" 查看数据
3. 右键表名，选择 "Properties" 查看表结构

### 主要数据表
- **个股人气表** (7,210条记录) - 股票人气排名数据
- **个股接力表** (709条记录) - 股票接力值数据  
- **个股板块关联表** (9,974条记录) - 股票板块关联关系
- **板块信息表** (2,247条记录) - 板块基础信息
- **个股连板高度表** (8,290条记录) - 涨停相关数据

## 🔍 常用操作

### 1. 执行SQL查询
1. 右键数据库名称，选择 "Query Tool"
2. 在查询编辑器中输入SQL语句
3. 点击 "Execute" (F5) 执行查询

### 2. 查看最新人气数据
```sql
SELECT * FROM "个股人气表"
WHERE "日期" = (SELECT MAX("日期") FROM "个股人气表")
ORDER BY "综合人气评分" DESC NULLS LAST
LIMIT 10;
```

### 3. 查看板块信息
```sql
SELECT "板块名称", "板块涨幅", "板块消息"
FROM "板块信息表"
WHERE "日期" = 20250304
ORDER BY CAST("板块涨幅" AS NUMERIC) DESC;
```

### 4. 导出数据
1. 执行查询后，右键结果表格
2. 选择 "Copy" → "Copy (with headers)" 复制到剪贴板
3. 或选择 "Download as CSV" 导出为CSV文件

## ⚙️ 高级配置

### 1. 性能优化
- **File** → **Preferences** → **Query Tool**
- 设置 "Query result set limit": `1000` (限制结果集大小)
- 勾选 "Auto commit": 自动提交事务

### 2. 界面定制
- **File** → **Preferences** → **Browser**
- 调整字体大小和主题
- 配置显示选项

### 3. 备份设置
- 右键数据库名称，选择 "Backup..."
- 选择备份格式和保存位置
- 建议定期备份重要数据

## 🔒 安全建议

### 1. 创建只读用户
```sql
-- 在pgAdmin查询工具中执行
CREATE USER readonly_user WITH PASSWORD 'your_secure_password';
GRANT CONNECT ON DATABASE tradefusion TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
```

### 2. 修改默认密码
```sql
-- 修改postgres用户密码
ALTER USER postgres PASSWORD 'new_secure_password';
```

## 🚨 故障排除

### 连接失败
1. **检查PostgreSQL服务**:
   - 打开服务管理器 (services.msc)
   - 确认 "postgresql-x64-13" 服务正在运行

2. **检查端口**:
   - 确认5432端口未被占用
   - 检查防火墙设置

3. **检查配置文件**:
   - 查看 `E:\TradeFusion\数据库_PostgreSQL\postgresql.conf`
   - 确认 `listen_addresses = 'localhost'`
   - 确认 `port = 5432`

### 权限问题
1. 确认用户名密码正确
2. 检查 `pg_hba.conf` 文件配置
3. 重启PostgreSQL服务

## 📞 技术支持

### 官方资源
- **pgAdmin官网**: https://www.pgadmin.org/
- **文档**: https://www.pgadmin.org/docs/
- **社区支持**: https://www.postgresql.org/community/

### TradeFusion项目
- **数据库配置**: `E:\TradeFusion\数据库_PostgreSQL\数据库连接说明.md`
- **项目配置**: `E:\TradeFusion\config\data_sources.yaml`

---

**安装完成后，您就可以通过pgAdmin的图形界面轻松管理和查看TradeFusion数据库了！**
