#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import time
import re
import random
import logging
import pandas as pd  # Keep for now, might remove if not strictly needed for final output
from pathlib import Path
from datetime import datetime

from logging.handlers import TimedRotatingFileHandler

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 将使用同步API替代异步API
from playwright.sync_api import sync_playwright
from 数据2_网络采集.浏览器管理器 import get_browser_manager

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器
logger = 获取日志器("人气_东财采集")

# 必须先初始化路径
def get_project_paths():
    """获取项目路径配置"""
    script_path = Path(__file__).absolute()
    # This script is in '数据2_网络采集', so project_root is one level up.
    project_root = script_path.parent.parent

    paths = {
        'root': project_root,
        'drivers': project_root / 'drivers', # Though not used in this script
        'logs': project_root / 'logs',       # Though not used in this script
        'crawler': project_root / '数据2_网络采集',
        'common': project_root / '公共模块'
    }
    
    # 确保目录存在
    for key, path_val in paths.items():
        # Only create 'crawler' and 'common' if they are actual paths, root is just a reference
        if key in ['crawler', 'common', 'logs', 'drivers']: # Ensure necessary operational dirs
            if not path_val.exists():
                path_val.mkdir(parents=True, exist_ok=True)
    
    return paths

PATHS = get_project_paths()
if str(PATHS['root']) not in sys.path:
    sys.path.insert(0, str(PATHS['root']))
if str(PATHS['common']) not in sys.path: # Ensure common is also in path if direct imports needed
    sys.path.insert(0, str(PATHS['common']))


# 现在可以安全导入其他模块
try:
    from 公共模块.交易日期 import get_trading_date
    from 公共模块.加代码前缀 import process_stock_code_prefix
except ImportError as e:



    sys.exit(1)


def scrape_stock_codes():
    """抓取东方财富热榜A股页面的股票代码和名称"""



    # 初始化数据存储列表
    stock_data_raw = []

    try:
        # 使用持久化浏览器管理器
        browser_manager = get_browser_manager("eastmoney")


        url = 'https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock'

        try:
            # 获取页面，自动处理浏览器创建和页面刷新
            page = browser_manager.get_page(url, force_refresh=True)


            # 等待页面稳定
            time.sleep(random.uniform(2, 4))

        except Exception as nav_error:

            # 尝试截图和保存页面源码用于调试
            try:
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                page = browser_manager.get_page()  # 获取当前页面进行截图
                screenshot_path = PATHS['logs'] / f"eastmoney_nav_error_{timestamp}.png"
                page.screenshot(path=str(screenshot_path))


                # 保存页面源码
                html_content = page.content()
                html_path = PATHS['logs'] / f"eastmoney_nav_error_{timestamp}.html"
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)

            except:
                pass
            raise

        # 增加等待时间确保页面完全加载
        time.sleep(3)

        # 模拟用户行为
        page.mouse.move(random.randint(100, 500), random.randint(100, 500))
        page.mouse.wheel(0, random.randint(300, 600))
        time.sleep(random.uniform(2, 4))

        page.wait_for_load_state("networkidle")
        time.sleep(3)  # 等待页面完全加载



        # 使用改进的JavaScript提取代码和名称
        stock_data = page.evaluate("""
                () => {
                    const stockData = [];
                    
                    // 查找所有股票项目
                    const items = document.querySelectorAll('.item');
                    
                    items.forEach(item => {
                        // 查找股票代码
                        const codeElement = item.querySelector('.code');
                        // 查找股票名称
                        const nameElement = item.querySelector('.stock-name span');
                        
                        if (codeElement && nameElement) {
                            const code = codeElement.textContent.trim();
                            const name = nameElement.textContent.trim();
                            
                            if (code && code.length === 6 && name) {
                                stockData.push({code, name});
                            }
                        } else {
                            // 尝试从其他属性获取
                            const stockCodeAttr = item.querySelector('[stockcode]');
                            const stockNameAttr = item.querySelector('[stockname]');
                            
                            if (stockCodeAttr && stockNameAttr) {
                                const stockCode = stockCodeAttr.getAttribute('stockcode');
                                const stockName = stockNameAttr.getAttribute('stockname');
                                
                                // 提取纯代码（去掉SZ/SH前缀）
                                const code = stockCode.substring(2);
                                const name = stockName;
                                
                                if (code && code.length === 6 && name) {
                                    stockData.push({code, name});
                                }
                            }
                        }
                    });
                    
                    // 如果上面的方法没有找到数据，尝试从属性中提取
                    if (stockData.length === 0) {
                        const elements = document.querySelectorAll('[stockcode][stockname]');
                        
                        elements.forEach(el => {
                            const stockCode = el.getAttribute('stockcode');
                            const stockName = el.getAttribute('stockname');
                            
                            if (stockCode && stockName) {
                                // 提取纯代码（去掉SZ/SH前缀）
                                const code = stockCode.substring(2);
                                const name = stockName;
                                
                                if (code && code.length === 6 && name) {
                                    stockData.push({code, name});
                                }
                            }
                        });
                    }
                    
                    // 去重，保留第一次出现的记录
                    const uniqueData = [];
                    const seenCodes = new Set();
                    
                    for (const item of stockData) {
                        if (!seenCodes.has(item.code)) {
                            seenCodes.add(item.code);
                            uniqueData.push(item);
                        }
                    }
                    
                    return uniqueData;
                }
        """)

        stock_data_raw = stock_data



        # 如果没有提取到数据，保存页面快照和源码用于调试
        if len(stock_data_raw) == 0:
            try:
                # 生成带时间戳的文件名，避免覆盖
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                screenshot_path = PATHS['logs'] / f"eastmoney_error_{timestamp}.png"
                page.screenshot(path=str(screenshot_path))


                # 获取页面源代码
                html_content = page.content()
                html_path = PATHS['logs'] / f"eastmoney_error_{timestamp}.html"
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)

            except Exception as ss_error:
                pass

            return []

        time.sleep(random.uniform(1, 2))

        # 注意：不再关闭浏览器，保持持久化连接
        
    except Exception as e:

        import traceback
        traceback.print_exc()
    
    return stock_data_raw

def _trigger_popularity_fusion():
    """触发个股人气表融合模块 - 临时禁用以解决双重进程问题"""
    # {{ AURA-X: Modify - 临时禁用人气融合模块调用，解决双重进程问题. Approval: 寸止(ID:1737734400). }}
    try:
        logger.记录模块执行("跳过人气融合模块调用（解耦测试）")
        return True

        # 原始代码（临时注释）：
        # from 数据2_网络采集.个股人气表 import main as 人气融合_main
        # result = 人气融合_main()
        # if result:
        #     pass
        # else:
        #     logger.记录错误(f"人气融合模块执行失败")

    except Exception as e:
        logger.记录错误(f"触发人气融合模块异常", e)

def main():
    start_time = time.time()
    try:
        random.seed(time.time())

        # 移除开始日志，只保留完成日志

        raw_stock_data = scrape_stock_codes()

        if raw_stock_data:
            trading_date = get_trading_date()
            processed_data_for_temp_table = []

            for i, stock in enumerate(raw_stock_data, 1):
                code = stock['code']
                name = stock['name']
                processed_code = process_stock_code_prefix(code)
                # rank is i
                processed_data_for_temp_table.append((processed_code, name, i))

            # {{ AURA-X: Modify - 临时禁用临时表写入，解决双重进程问题. Approval: 寸止(ID:1737734400). }}
            # 写入临时表（替代CSV文件） - 临时禁用
            try:
                # 临时禁用临时表写入
                logger.记录模块执行(f"跳过临时表写入（解耦测试）")

                # 原始代码（临时注释）：
                # from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
                # temp_manager = 获取临时表管理器()
                # temp_manager.写入东财数据(processed_data_for_temp_table)
                # temp_manager.close()

                # 记录采集成功（写入临时表）
                logger.记录模块执行(f"网站数据采集完成", len(raw_stock_data))

                # 🔄 东财数据采集完成后，立即触发人气融合模块
                _trigger_popularity_fusion()

            except Exception as e:
                logger.记录错误(f"写入临时表失败", e)
                return None
        else:
            logger.记录错误(f"网站数据采集失败，未获取到数据")
    except Exception as e:
        logger.记录错误(f"程序异常", e)
        import traceback
        traceback.print_exc()


def run_continuous():
    """循环运行模式：每60秒执行一次"""
    import time

    cycle_count = 0
    success_count = 0
    fail_count = 0

    # logger.info(f"🔄 [人气_东财采集] 循环模式启动 - 60秒间隔")

    try:
        while True:
            cycle_count += 1
            start_time = time.time()

            try:
                main()
                success_count += 1
                # logger.info(f"💓 [人气_东财采集] 第{cycle_count}次执行成功")
            except Exception as e:
                fail_count += 1
                logger.记录错误(f"第{cycle_count}次执行失败", e)

            # 计算等待时间（60秒）
            execution_time = time.time() - start_time
            sleep_time = max(0, 60 - execution_time)

            if sleep_time > 0:
                time.sleep(sleep_time)
            else:
                logger.记录错误(f"执行时间{execution_time:.1f}秒超过60秒间隔")

    except KeyboardInterrupt:
        # logger.info(f"<人气_东财采集> 用户中断，正在退出...")
        pass
    except Exception as e:
        logger.记录错误(f"循环运行异常", e)
    finally:
        # logger.info(f"💓 [人气_东财采集] 循环结束汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败")
        pass


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        # 单次执行模式
        main()
    else:
        # 循环执行模式（默认）
        run_continuous()