"""
TradeFusion配置管理模块
支持YAML配置文件和环境变量
"""
import os
import re
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# 暂时移除日志系统，稍后从全局角度重新设计


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config/data_sources.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径（相对于项目根目录）
        """
        self.project_root = Path(__file__).parent.parent
        self.config_file = self.project_root / config_file
        self._config = None
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if not self.config_file.exists():
                raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # 处理环境变量替换
                content = self._replace_env_vars(content)
                self._config = yaml.safe_load(content)
                
        except Exception as e:
            # 使用默认配置
            self._config = self._get_default_config()
    
    def _replace_env_vars(self, content: str) -> str:
        """
        替换配置文件中的环境变量
        支持语法: ${ENV_VAR:default_value} 或 ${ENV_VAR}
        """
        def replace_var(match):
            var_expr = match.group(1)
            if ':' in var_expr:
                var_name, default_value = var_expr.split(':', 1)
                return os.environ.get(var_name, default_value)
            else:
                return os.environ.get(var_expr, match.group(0))
        
        return re.sub(r'\$\{([^}]+)\}', replace_var, content)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'data_sources': {
                'dzh_base_path': 'E:/dzh2/USERDATA/SelfData',
                'directories': {
                    'lbgd': '10连板高度',
                    'ztdp': '12涨停评分',
                    'ztsj': '11涨停时间',
                    'yzb': '13一字板',
                    'tzb': '14T字板',
                    'hjhs': '15黄金换手',
                    'cjje': '22成交金额'
                }
            },
            'processing': {
                'file_timeout_hours': 20,  # {{ AURA-X: Modify - 延长文件超时时间至20小时. Approval: 寸止(ID:1735002700). }}
                'batch_size': 1000,
                'timezone': 'Asia/Shanghai'
            },
            # {{ AURA-X: Modify - 修改为PostgreSQL数据库配置. Approval: 寸止(ID:1737734400). }}
            # {{ AURA-X: Modify - 移除psycopg2不支持的字段，避免连接错误. Approval: 寸止(ID:1737734400). }}
            'database': {
                'host': 'localhost',
                'port': 5432,
                'database': 'tradefusion',
                'user': 'postgres',
                'password': 'ymjatTUU520'
            },
            'database_meta': {
                'type': 'postgresql',
                'data_path': '数据库_PostgreSQL'
            }
        }
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的路径

        Args:
            key_path: 配置键路径，如 'data_sources.dzh_base_path'
            default: 默认值

        Returns:
            配置值
        """
        # {{ AURA-X: Modify - 添加配置空值检查，修复数据库迁移遗留的类型安全问题. Approval: 寸止(ID:1737734400). }}
        if self._config is None:
            return default

        keys = key_path.split('.')
        value = self._config

        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_data_dirs(self) -> Dict[str, str]:
        """获取数据目录配置"""
        base_path = self.get('data_sources.dzh_base_path')
        directories = self.get('data_sources.directories', {})
        
        return {
            key: os.path.join(base_path, subdir)
            for key, subdir in directories.items()
        }
    
    def get_db_path(self) -> Path:
        """获取数据库文件路径"""
        db_path = self.get('database.db_path')
        return self.project_root / db_path
    
    def get_file_timeout_seconds(self) -> int:
        """获取文件时效性检查时间（秒）"""
        hours = self.get('processing.file_timeout_hours', 12)
        return hours * 3600
    
    def reload(self):
        """重新加载配置"""
        self._load_config()


# 全局配置实例
config = ConfigManager()


def get_config() -> ConfigManager:
    """获取全局配置实例"""
    return config
